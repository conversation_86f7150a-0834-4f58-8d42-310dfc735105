spring:
  redis:
    host: r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com
    port: 6379
    password: Db123456
    database: 119
    jedis:
      pool:
        max-idle: 5
        min-idle: 0
        max-active: 20
    timeout: 30s
  session:
    store-type: redis
  datasource:
    ## 保险业务数据库
    safes:
      name: safes
#      url: ********************************************************************************************************************************************************************************************************************************
#      username: safes
#      password: Zhnx#safes
      driverClassName: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 3
      minIdle: 1
      maxActive: 30
      maxWait: 180000
      timeBetweenEvictionRunsMillis: 180000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
    ## 数据回溯 慢库
    odps:
      name: odps
      url: *********************************************************************************************************
      username: LTAI4FkbeKUT5tvHK1vRCNu1
      password: ******************************
      driverClassName: org.postgresql.Driver
      initialSize: 3
      minIdle: 1
      maxActive: 30
      type: com.alibaba.druid.pool.DruidDataSource
      maxWait: 180000
      timeBetweenEvictionRunsMillis: 180000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
    # 数仓
    dw:
      url: *****************************************************************************************************************
      driverClassName: org.postgresql.Driver
      username: devops
      password: devops123
      initialSize: 3
      minIdle: 1
      maxActive: 30
      type: com.alibaba.druid.pool.DruidDataSource
      maxWait: 180000
      timeBetweenEvictionRunsMillis: 180000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20

online-date: 2020-09-01
insurance-admin:
  url: http://insurance-admin.tsg.cfpamf.com/
insurance-operation:
  url: http://insurance-operation.tsg.cfpamf.com/
bms:
  api:
    url: http://bms-service.tsg.cfpamf.com/

qbi:
  accessKeyId: 2a049bb5-fd7a-427f-b212-cda48c600e2c
  accessKeySecret: 2fb9e84f-7a2b-4721-924f-c1d6180acb85
  host: //qbi.cdfinance.com.cn
  userId: dev_center

# xxl-job配置
xxl:
  job:
    admin:
      addresses: http://xxl-job-admin.dev.cfpamf.com/xxl-job-admin
    executor:
      appname: insurance-report-behavior-log
      address:
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
    accessToken: default_token

# 阿里云SLS配置
aliyun:
  sls:
    endpoint: cn-beijing.log.aliyuncs.com
    accessKeyId: ${ALIYUN_ACCESS_KEY_ID:your-access-key-id}
    accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET:your-access-key-secret}
    securityToken: ${ALIYUN_SECURITY_TOKEN:}
    connectionTimeout: 30000
    readTimeout: 60000
    batchSize: 1000
    maxProcessSize: 10000
    cursorTimeout: 60
    pullInterval: 5000
    maxRetryCount: 3
    retryInterval: 1000
    enabled: true
    corePoolSize: 5
    maxPoolSize: 10
    queueCapacity: 100
    threadNamePrefix: sls-pull-