package com.cfpamf.ms.insur.report.service.behavior.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.log.common.FastLog;
import com.aliyun.openservices.log.common.FastLogContent;
import com.aliyun.openservices.log.common.FastLogGroup;
import com.aliyun.openservices.log.common.LogGroupData;
import com.cfpamf.ms.insur.report.pojo.po.UserBehaviorLog;
import com.cfpamf.ms.insur.report.service.behavior.interfaces.LogParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * 默认日志解析器实现
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Component
public class FastLogParser implements LogParser {

    @Override
    public List<UserBehaviorLog> parseLogGroup(Long taskId, LogGroupData logGroupData) {
        List<UserBehaviorLog> behaviorLogs = new ArrayList<>();
        if(logGroupData == null){
            return behaviorLogs;
        }
        FastLogGroup fastLogGroup = logGroupData.GetFastLogGroup();

        try {
            if (fastLogGroup == null || fastLogGroup.getLogsCount() == 0) {
                return behaviorLogs;
            }


            for (int i = 0; i < fastLogGroup.getLogsCount(); i++) {
                FastLog fastLog = fastLogGroup.getLogs(i);
                UserBehaviorLog behaviorLog = parseLog(taskId, fastLog);
                if (behaviorLog != null) {
                    behaviorLogs.add(behaviorLog);
                }
            }
        } catch (Exception e) {
            log.error("解析FastLogGroup失败，taskId: {}", taskId, e);
        }

        return behaviorLogs;
    }

    @Override
    public UserBehaviorLog parseLogContent(Long taskId, String logContent) {
        try {
            if (StringUtils.isBlank(logContent)) {
                return null;
            }

            JSONObject jsonLog = JSON.parseObject(logContent);
            return parseJsonToUserBehaviorLog(taskId, jsonLog, logContent);
        } catch (Exception e) {
            log.warn("解析日志内容失败，taskId: {}, logContent: {}", taskId, logContent, e);
            return null;
        }
    }

    @Override
    public boolean isValidLogFormat(String logContent) {
        try {
            if (StringUtils.isBlank(logContent)) {
                return false;
            }
            
            JSONObject jsonLog = JSON.parseObject(logContent);
            // 基本字段验证
            return jsonLog.containsKey("event_type") || jsonLog.containsKey("eventType") ||
                   jsonLog.containsKey("event_name") || jsonLog.containsKey("eventName");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 解析FastLog为用户行为日志
     *
     * @param taskId  任务ID
     * @param fastLog FastLog对象
     * @return 用户行为日志
     */
    private UserBehaviorLog parseLog(Long taskId, FastLog fastLog) {
        try {
            UserBehaviorLog behaviorLog = new UserBehaviorLog();
            behaviorLog.setTaskId(taskId);
            
            // 设置事件时间
            long timestamp = fastLog.getTime();
            LocalDateTime eventTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
            behaviorLog.setEventTime(eventTime);

            // 解析日志内容
            StringBuilder rawLogBuilder = new StringBuilder();
            JSONObject logData = new JSONObject();
            
            for (int j = 0; j < fastLog.getContentsCount(); j++) {
                FastLogContent content = fastLog.getContents(j);
                String key = content.getKey();
                String value = content.getValue();
                
                rawLogBuilder.append(key).append("=").append(value).append("; ");
                logData.put(key, value);
            }

            behaviorLog.setRawLogData(rawLogBuilder.toString());
            
            // 解析具体字段
            parseLogFields(behaviorLog, logData);
            
            return behaviorLog;
        } catch (Exception e) {
            log.warn("解析FastLog失败，taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 解析JSON对象为用户行为日志
     *
     * @param taskId     任务ID
     * @param jsonLog    JSON日志对象
     * @param rawContent 原始内容
     * @return 用户行为日志
     */
    private UserBehaviorLog parseJsonToUserBehaviorLog(Long taskId, JSONObject jsonLog, String rawContent) {
        UserBehaviorLog behaviorLog = new UserBehaviorLog();
        behaviorLog.setTaskId(taskId);
        behaviorLog.setRawLogData(rawContent);
        
        parseLogFields(behaviorLog, jsonLog);
        
        return behaviorLog;
    }

    /**
     * 解析日志字段
     *
     * @param behaviorLog 用户行为日志对象
     * @param logData     日志数据
     */
    private void parseLogFields(UserBehaviorLog behaviorLog, JSONObject logData) {
        // 用户相关信息
        behaviorLog.setUserId(getStringValue(logData, "user_id", "userId", "uid"));
        behaviorLog.setSessionId(getStringValue(logData, "session_id", "sessionId", "sid"));
        
        // 事件相关信息
        behaviorLog.setEventType(getStringValue(logData, "event_type", "eventType", "type"));
        behaviorLog.setEventName(getStringValue(logData, "event_name", "eventName", "name"));
        
        // 页面相关信息
        behaviorLog.setPageUrl(getStringValue(logData, "page_url", "pageUrl", "url"));
        behaviorLog.setPageTitle(getStringValue(logData, "page_title", "pageTitle", "title"));
        behaviorLog.setReferrerUrl(getStringValue(logData, "referrer_url", "referrerUrl", "referrer"));
        
        // 元素相关信息
        behaviorLog.setElementId(getStringValue(logData, "element_id", "elementId", "target_id"));
        behaviorLog.setElementText(getStringValue(logData, "element_text", "elementText", "target_text"));
        
        // 设备相关信息
        behaviorLog.setIpAddress(getStringValue(logData, "ip_address", "ipAddress", "ip"));
        behaviorLog.setUserAgent(getStringValue(logData, "user_agent", "userAgent", "ua"));
        behaviorLog.setDeviceType(getStringValue(logData, "device_type", "deviceType", "device"));
        behaviorLog.setBrowserType(getStringValue(logData, "browser_type", "browserType", "browser"));
        behaviorLog.setOsType(getStringValue(logData, "os_type", "osType", "os"));
        behaviorLog.setScreenResolution(getStringValue(logData, "screen_resolution", "screenResolution", "resolution"));
        
        // 时间相关信息
        behaviorLog.setDuration(getLongValue(logData, "duration", "time_spent"));
        
        // 解析事件时间
        if (behaviorLog.getEventTime() == null) {
            String timeStr = getStringValue(logData, "event_time", "eventTime", "timestamp", "time");
            if (StringUtils.isNotBlank(timeStr)) {
                try {
                    long timestamp = Long.parseLong(timeStr);
                    // 判断是秒还是毫秒
                    if (timestamp < 10000000000L) {
                        timestamp *= 1000;
                    }
                    LocalDateTime eventTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
                    behaviorLog.setEventTime(eventTime);
                } catch (Exception e) {
                    log.debug("解析事件时间失败: {}", timeStr);
                }
            }
        }
        
        // 设置自定义属性
        JSONObject customAttrs = new JSONObject();
        for (String key : logData.keySet()) {
            if (!isStandardField(key)) {
                customAttrs.put(key, logData.get(key));
            }
        }
        if (!customAttrs.isEmpty()) {
            behaviorLog.setCustomAttributes(customAttrs.toJSONString());
        }
    }

    /**
     * 获取字符串值（支持多个可能的字段名）
     */
    private String getStringValue(JSONObject jsonObject, String... keys) {
        for (String key : keys) {
            String value = jsonObject.getString(key);
            if (StringUtils.isNotBlank(value)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取长整型值
     */
    private Long getLongValue(JSONObject jsonObject, String... keys) {
        for (String key : keys) {
            try {
                Long value = jsonObject.getLong(key);
                if (value != null) {
                    return value;
                }
            } catch (Exception ignored) {
            }
        }
        return null;
    }

    /**
     * 判断是否为标准字段
     */
    private boolean isStandardField(String key) {
        return key.equals("user_id") || key.equals("userId") || key.equals("uid") ||
               key.equals("session_id") || key.equals("sessionId") || key.equals("sid") ||
               key.equals("event_type") || key.equals("eventType") || key.equals("type") ||
               key.equals("event_name") || key.equals("eventName") || key.equals("name") ||
               key.equals("page_url") || key.equals("pageUrl") || key.equals("url") ||
               key.equals("page_title") || key.equals("pageTitle") || key.equals("title") ||
               key.equals("element_id") || key.equals("elementId") || key.equals("target_id") ||
               key.equals("element_text") || key.equals("elementText") || key.equals("target_text") ||
               key.equals("event_time") || key.equals("eventTime") || key.equals("timestamp") || key.equals("time") ||
               key.equals("ip_address") || key.equals("ipAddress") || key.equals("ip") ||
               key.equals("user_agent") || key.equals("userAgent") || key.equals("ua") ||
               key.equals("device_type") || key.equals("deviceType") || key.equals("device") ||
               key.equals("browser_type") || key.equals("browserType") || key.equals("browser") ||
               key.equals("os_type") || key.equals("osType") || key.equals("os") ||
               key.equals("screen_resolution") || key.equals("screenResolution") || key.equals("resolution") ||
               key.equals("referrer_url") || key.equals("referrerUrl") || key.equals("referrer") ||
               key.equals("duration") || key.equals("time_spent");
    }
}
