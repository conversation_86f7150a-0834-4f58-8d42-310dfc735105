package com.cfpamf.ms.insur.report.service.behavior.job;

import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTask;
import com.cfpamf.ms.insur.report.service.behavior.config.SlsConfig;
import com.cfpamf.ms.insur.report.service.behavior.interfaces.BehaviorLogTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 用户行为日志拉取定时任务
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Component
public class BehaviorLogPullJob {

    @Autowired
    private BehaviorLogTaskService behaviorLogTaskService;

    @Autowired
    private SlsConfig slsConfig;

    /**
     * 线程池用于并发执行任务
     */
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 执行日志拉取任务
     * 定时任务：每5分钟执行一次，查询需要执行的任务并启动
     */
    @XxlJob("behaviorLogPullHandler")
    public ReturnT<String> behaviorLogPullHandler(String param) {
        log.info("开始执行用户行为日志拉取定时任务，参数: {}", param);
        
        try {
            // 检查功能是否启用
            if (!slsConfig.getEnabled()) {
                log.info("用户行为日志拉取功能已禁用");
                return ReturnT.SUCCESS;
            }

            // 查询需要执行的任务
            List<BehaviorLogTask> executableTasks = behaviorLogTaskService.getExecutableTasks();
            if (CollectionUtils.isEmpty(executableTasks)) {
                log.info("没有需要执行的日志拉取任务");
                return ReturnT.SUCCESS;
            }

            log.info("找到 {} 个需要执行的日志拉取任务", executableTasks.size());

            // 并发执行任务
            int successCount = 0;
            int failCount = 0;

            for (BehaviorLogTask task : executableTasks) {
                try {
                    // 检查任务是否已在执行
                    if (behaviorLogTaskService.isTaskRunning(task.getId())) {
                        log.info("任务已在执行中，跳过，taskId: {}, taskName: {}", 
                                task.getId(), task.getTaskName());
                        continue;
                    }

                    // 检查任务是否需要执行（根据时间条件）
                    if (!shouldExecuteTask(task)) {
                        log.debug("任务暂不需要执行，taskId: {}, taskName: {}", 
                                task.getId(), task.getTaskName());
                        continue;
                    }

                    // 异步启动任务
                    CompletableFuture.runAsync(() -> {
                        try {
                            log.info("启动日志拉取任务，taskId: {}, taskName: {}", 
                                    task.getId(), task.getTaskName());
                            boolean success = behaviorLogTaskService.startTask(task.getId());
                            if (success) {
                                log.info("任务启动成功，taskId: {}", task.getId());
                            } else {
                                log.warn("任务启动失败，taskId: {}", task.getId());
                            }
                        } catch (Exception e) {
                            log.error("启动任务异常，taskId: {}", task.getId(), e);
                        }
                    }, executorService);

                    successCount++;
                    
                    // 控制并发数，避免同时启动过多任务
                    if (successCount % 3 == 0) {
                        Thread.sleep(1000);
                    }

                } catch (Exception e) {
                    log.error("处理任务异常，taskId: {}, taskName: {}", 
                            task.getId(), task.getTaskName(), e);
                    failCount++;
                }
            }

            String result = String.format("定时任务执行完成，成功启动: %d 个任务，失败: %d 个任务", 
                    successCount, failCount);
            log.info(result);
            
            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("执行用户行为日志拉取定时任务异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "定时任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 清理过期日志定时任务
     * 定时任务：每天凌晨2点执行，清理超过30天的日志数据
     */
    @XxlJob("behaviorLogCleanHandler")
    public ReturnT<String> behaviorLogCleanHandler(String param) {
        log.info("开始执行用户行为日志清理定时任务，参数: {}", param);
        
        try {
            // 默认清理30天前的数据，可通过参数配置
            int retentionDays = 30;
            if (param != null && param.matches("\\d+")) {
                retentionDays = Integer.parseInt(param);
            }

            LocalDateTime beforeTime = LocalDateTime.now().minusDays(retentionDays);
            
            // 这里可以调用相应的清理方法
            // int deletedCount = behaviorLogMapper.deleteExpiredLogs(beforeTime);
            
            String result = String.format("日志清理任务执行完成，清理 %d 天前的数据", retentionDays);
            log.info(result);
            
            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("执行用户行为日志清理定时任务异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "清理任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 任务状态监控定时任务
     * 定时任务：每10分钟执行一次，监控长时间运行的任务
     */
    @XxlJob("behaviorLogMonitorHandler")
    public ReturnT<String> behaviorLogMonitorHandler(String param) {
        log.info("开始执行用户行为日志任务监控定时任务，参数: {}", param);
        
        try {
            // 查询进行中的任务
            List<BehaviorLogTask> runningTasks = behaviorLogTaskService.getTasksByStatus(
                    BehaviorLogTask.TaskStatus.IN_PROGRESS.getCode());
            
            if (CollectionUtils.isEmpty(runningTasks)) {
                log.info("没有正在执行的任务需要监控");
                return ReturnT.SUCCESS;
            }

            int monitoredCount = 0;
            int timeoutCount = 0;

            for (BehaviorLogTask task : runningTasks) {
                try {
                    // 检查任务是否超时（运行超过2小时）
                    LocalDateTime startTime = task.getUpdatedTime();
                    if (startTime != null && startTime.isBefore(LocalDateTime.now().minusHours(2))) {
                        log.warn("发现超时任务，taskId: {}, taskName: {}, 开始时间: {}", 
                                task.getId(), task.getTaskName(), startTime);
                        
                        // 可以选择停止超时任务或发送告警
                        // behaviorLogTaskService.stopTask(task.getId());
                        timeoutCount++;
                    }

                    // 记录任务进度
                    int progress = behaviorLogTaskService.getTaskProgress(task.getId());
                    log.info("任务执行进度，taskId: {}, taskName: {}, 进度: {}%", 
                            task.getId(), task.getTaskName(), progress);
                    
                    monitoredCount++;

                } catch (Exception e) {
                    log.error("监控任务异常，taskId: {}", task.getId(), e);
                }
            }

            String result = String.format("任务监控完成，监控任务数: %d，超时任务数: %d", 
                    monitoredCount, timeoutCount);
            log.info(result);
            
            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("执行用户行为日志任务监控定时任务异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "监控任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 判断任务是否需要执行
     *
     * @param task 任务信息
     * @return 是否需要执行
     */
    private boolean shouldExecuteTask(BehaviorLogTask task) {
        // 检查任务状态
        if (!BehaviorLogTask.TaskStatus.NOT_STARTED.getCode().equals(task.getTaskStatus()) &&
            !BehaviorLogTask.TaskStatus.IN_PROGRESS.getCode().equals(task.getTaskStatus())) {
            return false;
        }

        // 检查开始时间
        if (task.getStartTime() != null && task.getStartTime().isAfter(LocalDateTime.now())) {
            return false;
        }

        // 检查结束时间
        if (task.getEndTime() != null && task.getEndTime().isBefore(LocalDateTime.now())) {
            return false;
        }

        return true;
    }

    /**
     * 应用关闭时清理资源
     */
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
