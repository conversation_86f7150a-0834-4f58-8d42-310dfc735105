package com.cfpamf.ms.insur.report.web.admin;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTask;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTaskDetail;
import com.cfpamf.ms.insur.report.pojo.po.UserBehaviorLog;
import com.cfpamf.ms.insur.report.service.behavior.interfaces.BehaviorLogTaskService;
import com.cfpamf.ms.insur.report.dao.safepg.UserBehaviorLogMapper;
import com.cfpamf.ms.insur.report.service.behavior.monitor.MemoryMonitor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 用户行为日志控制器
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@RestController
@RequestMapping("/api/behavior-log")
@Api(tags = "用户行为日志管理")
public class BehaviorLogController {

    @Autowired
    private BehaviorLogTaskService behaviorLogTaskService;

    @Autowired
    private UserBehaviorLogMapper userBehaviorLogMapper;

    @Autowired
    private MemoryMonitor memoryMonitor;

    /**
     * 创建日志拉取任务
     */
    @PostMapping("/task")
    @ApiOperation("创建日志拉取任务")
    public BehaviorLogTask createTask(@RequestBody BehaviorLogTask task) {
        try {
            log.info("创建日志拉取任务请求，taskName: {}", task.getTaskName());
            
            // 设置创建人
            task.setCreatedBy("admin");
            task.setUpdatedBy("admin");
            
            BehaviorLogTask createdTask = behaviorLogTaskService.createTask(task);
            return createdTask;
        } catch (Exception e) {
            log.warn("创建日志拉取任务失败", e);
            throw new MSBizNormalException("","创建日志拉取任务失败");
        }
    }

    /**
     * 查询所有任务
     */
    @GetMapping("/task/list")
    @ApiOperation("查询所有任务")
    public List<BehaviorLogTask> getAllTasks() {
        try {
            List<BehaviorLogTask> tasks = behaviorLogTaskService.getAllTasks();
            return tasks;
        } catch (Exception e) {
            log.warn("查询所有任务失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据ID查询任务
     */
    @GetMapping("/task/{taskId}")
    @ApiOperation("根据ID查询任务")
    public BehaviorLogTask getTaskById(@PathVariable @ApiParam("任务ID") Long taskId) {
        try {
            BehaviorLogTask task = behaviorLogTaskService.getTaskById(taskId);
            return task;
        } catch (Exception e) {
            log.warn("查询任务失败，taskId: {}", taskId, e);
            throw new MSBizNormalException("","查询任务失败");

        }
    }

    /**
     * 启动任务
     */
    @PostMapping("/task/{taskId}/start")
    @ApiOperation("启动任务")
    public String startTask(@PathVariable @ApiParam("任务ID") Long taskId) {
        try {
            log.info("启动任务请求，taskId: {}", taskId);
            boolean success = behaviorLogTaskService.startTask(taskId);
            if (success) {
                return "任务启动成功";
            } else {
                return "任务启动失败";
            }
        } catch (Exception e) {
            log.warn("启动任务失败，taskId: {}", taskId, e);
            return "启动任务失败";
        }
    }

    /**
     * 停止任务
     */
    @PostMapping("/task/{taskId}/stop")
    @ApiOperation("停止任务")
    public String stopTask(@PathVariable @ApiParam("任务ID") Long taskId) {
        try {
            log.info("停止任务请求，taskId: {}", taskId);
            boolean success = behaviorLogTaskService.stopTask(taskId);
            if (success) {
                return "任务停止成功";
            } else {
                return "任务停止失败";
            }
        } catch (Exception e) {
            log.warn("停止任务失败，taskId: {}", taskId, e);
            return  "停止任务失败" ;
        }
    }

    /**
     * 重启任务
     */
    @PostMapping("/task/{taskId}/restart")
    @ApiOperation("重启任务")
    public String restartTask(@PathVariable @ApiParam("任务ID") Long taskId) {
        try {
            log.info("重启任务请求，taskId: {}", taskId);
            boolean success = behaviorLogTaskService.restartTask(taskId);
            if (success) {
                return "任务重启成功";
            } else {
                return "任务重启失败";
            }
        } catch (Exception e) {
            log.error("重启任务失败，taskId: {}", taskId, e);
            return "重启任务失败: ";
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/task/{taskId}")
    @ApiOperation("删除任务")
    public String deleteTask(@PathVariable @ApiParam("任务ID") Long taskId) {
        try {
            log.info("删除任务请求，taskId: {}", taskId);
            boolean success = behaviorLogTaskService.deleteTask(taskId);
            if (success) {
                return "任务删除成功";
            } else {
                return "任务删除失败";
            }
        } catch (Exception e) {
            log.warn("删除任务失败，taskId: {}", taskId, e);
            return "删除任务失败: " ;
        }
    }

    /**
     * 查询任务明细
     */
    @GetMapping("/task/{taskId}/details")
    @ApiOperation("查询任务明细")
    public List<BehaviorLogTaskDetail> getTaskDetails(@PathVariable @ApiParam("任务ID") Long taskId) {
        try {
            List<BehaviorLogTaskDetail> details = behaviorLogTaskService.getTaskDetails(taskId);
            return details;
        } catch (Exception e) {
            log.warn("查询任务明细失败，taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 查询任务进度
     */
    @GetMapping("/task/{taskId}/progress")
    @ApiOperation("查询任务进度")
    public Integer getTaskProgress(@PathVariable @ApiParam("任务ID") Long taskId) {
        try {
            int progress = behaviorLogTaskService.getTaskProgress(taskId);
            return progress;
        } catch (Exception e) {
            log.warn("查询任务进度失败，taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/task/{taskId}/status")
    @ApiOperation("查询任务状态")
    public String getTaskStatus(@PathVariable @ApiParam("任务ID") Long taskId) {
        try {
            boolean isRunning = behaviorLogTaskService.isTaskRunning(taskId);
            BehaviorLogTask task = behaviorLogTaskService.getTaskById(taskId);
            
            String status = "未知";
            if (task != null) {
                BehaviorLogTask.TaskStatus taskStatus = BehaviorLogTask.TaskStatus.fromCode(task.getTaskStatus());
                status = taskStatus != null ? taskStatus.getDescription() : "未知";
                
                if (isRunning) {
                    status += "(运行中)";
                }
            }
            
            return status;
        } catch (Exception e) {
            log.error("查询任务状态失败，taskId: {}", taskId, e);
            return "查询任务状态失败: ";
        }
    }

    /**
     * 查询用户行为日志
     */
    @GetMapping("/logs")
    @ApiOperation("查询用户行为日志")
    public List<UserBehaviorLog> getUserBehaviorLogs(
            @RequestParam(required = false) @ApiParam("任务ID") Long taskId,
            @RequestParam(required = false) @ApiParam("用户ID") String userId,
            @RequestParam(defaultValue = "10") @ApiParam("查询数量") Integer limit) {
        try {
            List<UserBehaviorLog> logs;
            if (taskId != null) {
                logs = userBehaviorLogMapper.findByTaskId(taskId);
            } else if (userId != null) {
                logs = userBehaviorLogMapper.findByUserId(userId);
            } else {
                // 查询最近的日志
                LocalDateTime endTime = LocalDateTime.now();
                LocalDateTime startTime = endTime.minusDays(1);
                logs = userBehaviorLogMapper.findByTimeRange(startTime, endTime);
            }
            
            // 限制返回数量
            if (logs.size() > limit) {
                logs = logs.subList(0, limit);
            }
            
            return logs;
        } catch (Exception e) {
            log.warn("查询用户行为日志失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 查询内存使用情况
     */
    @GetMapping("/memory/info")
    @ApiOperation("查询内存使用情况")
    public MemoryMonitor.MemoryInfo getMemoryInfo() {
        try {
            MemoryMonitor.MemoryInfo memoryInfo = memoryMonitor.getMemoryInfo();
            return memoryInfo;
        } catch (Exception e) {
            log.warn("查询内存使用情况失败", e);
            return null;
        }
    }

    /**
     * 强制垃圾回收
     */
    @PostMapping("/memory/gc")
    @ApiOperation("强制垃圾回收")
    public String forceGarbageCollection() {
        try {
            memoryMonitor.forceGarbageCollection();
            return "垃圾回收执行成功";
        } catch (Exception e) {
            log.error("执行垃圾回收失败", e);
            return "执行垃圾回收失败: " ;
        }
    }
}
