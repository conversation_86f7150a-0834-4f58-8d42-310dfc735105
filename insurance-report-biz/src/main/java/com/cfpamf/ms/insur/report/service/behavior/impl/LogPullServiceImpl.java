package com.cfpamf.ms.insur.report.service.behavior.impl;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.*;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.request.GetCursorRequest;
import com.aliyun.openservices.log.request.PullLogsRequest;
import com.aliyun.openservices.log.response.GetCursorResponse;
import com.aliyun.openservices.log.response.ListShardResponse;
import com.aliyun.openservices.log.response.PullLogsResponse;
import com.cfpamf.ms.insur.report.dao.safepg.BehaviorLogTaskDetailMapper;
import com.cfpamf.ms.insur.report.dao.safepg.BehaviorLogTaskMapper;
import com.cfpamf.ms.insur.report.dao.safepg.UserBehaviorLogMapper;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTask;
import com.cfpamf.ms.insur.report.pojo.po.BehaviorLogTaskDetail;
import com.cfpamf.ms.insur.report.pojo.po.UserBehaviorLog;
import com.cfpamf.ms.insur.report.service.behavior.config.SlsConfig;
import com.cfpamf.ms.insur.report.service.behavior.factory.SlsClientFactory;
import com.cfpamf.ms.insur.report.service.behavior.interfaces.LogParser;
import com.cfpamf.ms.insur.report.service.behavior.interfaces.LogPullService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 日志拉取服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Service
public class LogPullServiceImpl implements LogPullService {

    @Autowired
    private SlsClientFactory slsClientFactory;

    @Autowired
    private SlsConfig slsConfig;

    @Autowired
    private BehaviorLogTaskMapper taskMapper;

    @Autowired
    private BehaviorLogTaskDetailMapper taskDetailMapper;

    @Autowired
    private UserBehaviorLogMapper behaviorLogMapper;

    @Autowired
    private LogParser logParser;

    /**
     * 正在执行的任务标记
     */
    private final ConcurrentHashMap<Long, AtomicBoolean> runningTasks = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeTask(BehaviorLogTask task) {
        if (task == null || task.getId() == null) {
            log.warn("任务为空或任务ID为空");
            return false;
        }

        Long taskId = task.getId();
        
        // 检查任务是否已在执行
        if (isTaskRunning(taskId)) {
            log.warn("任务正在执行中，taskId: {}", taskId);
            return false;
        }

        // 标记任务开始执行
        runningTasks.put(taskId, new AtomicBoolean(true));
        
        try {
            log.info("开始执行日志拉取任务，taskId: {}, projectName: {}, logstoreName: {}", 
                    taskId, task.getProjectName(), task.getLogstoreName());

            // 更新任务状态为进行中
            taskMapper.updateTaskStatus(taskId, BehaviorLogTask.TaskStatus.IN_PROGRESS.getCode(), "system");

            // 获取SLS客户端
            Client client = slsClientFactory.getClient();

            // 获取分片列表
            List<Shard> shards = getShards(client, task.getProjectName(), task.getLogstoreName());
            if (CollectionUtils.isEmpty(shards)) {
                log.warn("未获取到分片信息，taskId: {}", taskId);
                taskMapper.updateTaskStatus(taskId, BehaviorLogTask.TaskStatus.FAILED.getCode(), "system");
                return false;
            }

            // 初始化任务明细
            initTaskDetails(taskId, shards);

            // 执行日志拉取
            boolean success = pullLogsFromShards(client, task, shards);

            // 更新任务状态
            if (success) {
                taskMapper.updateTaskStatus(taskId, BehaviorLogTask.TaskStatus.COMPLETED.getCode(), "system");
                log.info("日志拉取任务执行成功，taskId: {}", taskId);
            } else {
                taskMapper.updateTaskStatus(taskId, BehaviorLogTask.TaskStatus.FAILED.getCode(), "system");
                log.warn("日志拉取任务执行失败，taskId: {}", taskId);
            }

            return success;
        } catch (Exception e) {
            log.error("执行日志拉取任务异常，taskId: {}", taskId, e);
            taskMapper.updateTaskStatus(taskId, BehaviorLogTask.TaskStatus.FAILED.getCode(), "system");
            return false;
        } finally {
            // 移除任务执行标记
            runningTasks.remove(taskId);
        }
    }

    @Override
    public boolean stopTask(Long taskId) {
        AtomicBoolean running = runningTasks.get(taskId);
        if (running != null) {
            running.set(false);
            log.info("任务停止信号已发送，taskId: {}", taskId);
            return true;
        }
        return false;
    }

    @Override
    public boolean isTaskRunning(Long taskId) {
        AtomicBoolean running = runningTasks.get(taskId);
        return running != null && running.get();
    }

    @Override
    public int getTaskProgress(Long taskId) {
        try {
            List<BehaviorLogTaskDetail> details = taskDetailMapper.findByTaskId(taskId);
            if (CollectionUtils.isEmpty(details)) {
                return 0;
            }

            int completedCount = 0;
            for (BehaviorLogTaskDetail detail : details) {
                if (BehaviorLogTaskDetail.DetailStatus.COMPLETED.getCode().equals(detail.getStatus())) {
                    completedCount++;
                }
            }

            return (completedCount * 100) / details.size();
        } catch (Exception e) {
            log.warn("获取任务进度失败，taskId: {}", taskId, e);
            return 0;
        }
    }

    /**
     * 获取分片列表
     */
    private List<Shard> getShards(Client client, String projectName, String logstoreName) throws LogException {
        ListShardResponse response = client.ListShard(projectName,logstoreName);
        return response.GetShards();
    }

    /**
     * 初始化任务明细
     */
    private void initTaskDetails(Long taskId, List<Shard> shards) {
        // 查询已存在的任务明细
        List<BehaviorLogTaskDetail> existingDetails = taskDetailMapper.findByTaskId(taskId);
        
        List<BehaviorLogTaskDetail> newDetails = new ArrayList<>();
        for (Shard shard : shards) {
            int shardId = shard.getShardId();
            
            // 检查是否已存在该分片的明细
            boolean exists = existingDetails.stream()
                    .anyMatch(detail -> detail.getShardId().equals(shardId));
            
            if (!exists) {
                BehaviorLogTaskDetail detail = new BehaviorLogTaskDetail();
                detail.setTaskId(taskId);
                detail.setShardId(shardId);
                detail.setStatus(BehaviorLogTaskDetail.DetailStatus.NOT_STARTED.getCode());
                detail.setProcessedCount(0L);
                newDetails.add(detail);
            }
        }

        // 批量插入新的任务明细
        if (!CollectionUtils.isEmpty(newDetails)) {
            taskDetailMapper.batchInsert(newDetails);
            log.info("初始化任务明细完成，taskId: {}, 新增明细数: {}", taskId, newDetails.size());
        }
    }

    /**
     * 从分片拉取日志
     */
    private boolean pullLogsFromShards(Client client, BehaviorLogTask task, List<Shard> shards) {
        boolean allSuccess = true;

        for (Shard shard : shards) {
            // 检查任务是否被停止
            if (!isTaskRunning(task.getId())) {
                log.info("任务被停止，taskId: {}", task.getId());
                break;
            }

            try {
                boolean success = pullLogsFromShard(client, task, shard.getShardId());
                if (!success) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                log.error("从分片拉取日志失败，taskId: {}, shardId: {}", task.getId(), shard.getShardId(), e);
                allSuccess = false;
            }
        }

        return allSuccess;
    }

    /**
     * 从单个分片拉取日志
     */
    private boolean pullLogsFromShard(Client client, BehaviorLogTask task, int shardId) {
        Long taskId = task.getId();

        try {
            log.info("开始从分片拉取日志，taskId: {}, shardId: {}", taskId, shardId);

            // 获取任务明细
            BehaviorLogTaskDetail detail = taskDetailMapper.findByTaskIdAndShardId(taskId, shardId);
            if (detail == null) {
                log.warn("未找到任务明细，taskId: {}, shardId: {}", taskId, shardId);
                return false;
            }

            // 更新明细状态为进行中
            taskDetailMapper.updateProcessStatus(detail.getId(),
                    BehaviorLogTaskDetail.DetailStatus.IN_PROGRESS.getCode(),
                    detail.getProcessedCount(), null);

            // 获取游标
            String cursor = getCursor(client, task, shardId, detail);
            if (StringUtils.isBlank(cursor)) {
                log.warn("获取游标失败，taskId: {}, shardId: {}", taskId, shardId);
                taskDetailMapper.updateProcessStatus(detail.getId(),
                        BehaviorLogTaskDetail.DetailStatus.FAILED.getCode(),
                        detail.getProcessedCount(), "获取游标失败");
                return false;
            }

            // 拉取日志
            long processedCount = detail.getProcessedCount();
            String currentCursor = cursor;

            while (isTaskRunning(taskId)) {
                PullLogsResponse response = pullLogs(client, task, shardId, currentCursor);
                if (response == null) {
                    break;
                }

                // 解析并保存日志
                List<UserBehaviorLog> behaviorLogs = parseAndSaveLogs(taskId, response);
                processedCount += behaviorLogs.size();

                // 更新游标和处理数量
                String nextCursor = response.getNextCursor();
                taskDetailMapper.updateCursor(detail.getId(), currentCursor, nextCursor);
                taskDetailMapper.updateProcessStatus(detail.getId(),
                        BehaviorLogTaskDetail.DetailStatus.IN_PROGRESS.getCode(),
                        processedCount, null);

                // 检查是否还有更多日志
                if (StringUtils.isBlank(nextCursor) || nextCursor.equals(currentCursor)) {
                    log.info("分片日志拉取完成，taskId: {}, shardId: {}, processedCount: {}",
                            taskId, shardId, processedCount);
                    break;
                }

                currentCursor = nextCursor;

                // 防止过快拉取
                Thread.sleep(slsConfig.getPullInterval());
            }

            // 更新明细状态为完成
            taskDetailMapper.updateProcessStatus(detail.getId(),
                    BehaviorLogTaskDetail.DetailStatus.COMPLETED.getCode(),
                    processedCount, null);

            log.info("分片日志拉取成功，taskId: {}, shardId: {}, totalProcessed: {}",
                    taskId, shardId, processedCount);
            return true;

        } catch (Exception e) {
            log.error("从分片拉取日志异常，taskId: {}, shardId: {}", taskId, shardId, e);

            // 更新明细状态为失败
            BehaviorLogTaskDetail detail = taskDetailMapper.findByTaskIdAndShardId(taskId, shardId);
            if (detail != null) {
                taskDetailMapper.updateProcessStatus(detail.getId(),
                        BehaviorLogTaskDetail.DetailStatus.FAILED.getCode(),
                        detail.getProcessedCount(), e.getMessage());
            }
            return false;
        }
    }

    /**
     * 获取游标
     */
    private String getCursor(Client client, BehaviorLogTask task, int shardId, BehaviorLogTaskDetail detail)
            throws LogException {

        // 如果明细中已有游标，直接使用
        if (StringUtils.isNotBlank(detail.getCursorValue())) {
            return detail.getCursorValue();
        }

        // 根据任务开始时间获取游标
        long startTime = task.getStartTime().atZone(ZoneId.systemDefault()).toEpochSecond();

        GetCursorRequest request = new GetCursorRequest(task.getProjectName(),
                task.getLogstoreName(), shardId, startTime);
        GetCursorResponse response = client.GetCursor(request);

        return response.GetCursor();
    }

    /**
     * 拉取日志
     */
    private PullLogsResponse pullLogs(Client client, BehaviorLogTask task, int shardId, String cursor) {
        try {
            PullLogsRequest request = new PullLogsRequest(task.getProjectName(),
                    task.getLogstoreName(), shardId,slsConfig.getBatchSize(), cursor);
            request.setPullMode("scan_on_stream");
            // 设置查询条件
            if (StringUtils.isNotBlank(task.getQueryCondition())) {
                request.setQuery(task.getQueryCondition());
            }

            return client.pullLogs(request);
        } catch (Exception e) {
            log.warn("拉取日志失败，taskId: {}, shardId: {}, cursor: {}",
                    task.getId(), shardId, cursor, e);
            return null;
        }
    }

    /**
     * 解析并保存日志
     */
    private List<UserBehaviorLog> parseAndSaveLogs(Long taskId, PullLogsResponse response) {
        List<UserBehaviorLog> behaviorLogs = new ArrayList<>();

        try {
            if (response.getLogGroups() == null || response.getLogGroups().isEmpty()) {
                return behaviorLogs;
            }

            for (LogGroupData logGroup : response.getLogGroups()) {
                List<UserBehaviorLog> parsedLogs = logParser.parseLogGroup(taskId, logGroup);
                behaviorLogs.addAll(parsedLogs);
            }

            // 批量保存日志（分批处理防止内存溢出）
            if (!behaviorLogs.isEmpty()) {
                saveBehaviorLogsInBatches(behaviorLogs);
            }

        } catch (Exception e) {
            log.error("解析并保存日志失败，taskId: {}", taskId, e);
        }

        return behaviorLogs;
    }

    /**
     * 分批保存用户行为日志
     */
    private void saveBehaviorLogsInBatches(List<UserBehaviorLog> behaviorLogs) {
        int batchSize = slsConfig.getBatchSize();
        int totalSize = behaviorLogs.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<UserBehaviorLog> batch = behaviorLogs.subList(i, endIndex);

            try {
                behaviorLogMapper.batchInsert(batch);
                log.debug("批量保存用户行为日志成功，数量: {}", batch.size());
            } catch (Exception e) {
                log.error("批量保存用户行为日志失败，数量: {}", batch.size(), e);
            }
        }
    }
}
