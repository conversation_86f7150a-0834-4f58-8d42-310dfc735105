package com.cfpamf.ms.insur.report.service.behavior.factory;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.exception.LogException;
import com.cfpamf.ms.insur.report.service.behavior.config.SlsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * SLS客户端工厂类
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Component
public class SlsClientFactory {

    @Autowired
    private SlsConfig slsConfig;

    /**
     * 客户端缓存
     */
    private final ConcurrentHashMap<String, Client> clientCache = new ConcurrentHashMap<>();

    /**
     * 客户端健康检查调度器
     */
    private final ScheduledExecutorService healthCheckScheduler = Executors.newScheduledThreadPool(1);

    /**
     * 获取SLS客户端
     *
     * @return SLS客户端
     */
    public Client getClient() {
        return getClient(slsConfig.getEndpoint());
    }

    /**
     * 根据端点获取SLS客户端
     *
     * @param endpoint 端点
     * @return SLS客户端
     */
    public Client getClient(String endpoint) {
        return clientCache.computeIfAbsent(endpoint, this::createClient);
    }

    /**
     * 创建SLS客户端
     *
     * @param endpoint 端点
     * @return SLS客户端
     */
    private Client createClient(String endpoint) {
        try {
            log.info("创建SLS客户端，端点：{}", endpoint);
            
            Client client;
            if (slsConfig.getSecurityToken() != null && !slsConfig.getSecurityToken().isEmpty()) {
                // 使用STS临时凭证
                client = new Client(endpoint, slsConfig.getAccessKeyId(), 
                                  slsConfig.getAccessKeySecret(), slsConfig.getSecurityToken());
            } else {
                // 使用长期凭证
                client = new Client(endpoint, slsConfig.getAccessKeyId(), slsConfig.getAccessKeySecret());
            }
            
            // 设置超时时间
            client.setConnectionTimeout(slsConfig.getConnectionTimeout());
            client.setReadTimeout(slsConfig.getReadTimeout());
            
            log.info("SLS客户端创建成功，端点：{}", endpoint);
            return client;
        } catch (Exception e) {
            log.error("创建SLS客户端失败，端点：{}", endpoint, e);
            throw new RuntimeException("创建SLS客户端失败", e);
        }
    }

    /**
     * 关闭指定端点的客户端
     *
     * @param endpoint 端点
     */
    public void closeClient(String endpoint) {
        Client client = clientCache.remove(endpoint);
        if (client != null) {
            try {
                client.shutdown();
                log.info("SLS客户端已关闭，端点：{}", endpoint);
            } catch (Exception e) {
                log.warn("关闭SLS客户端失败，端点：{}", endpoint, e);
            }
        }
    }

    /**
     * 关闭所有客户端
     */
    @PreDestroy
    public void closeAllClients() {
        log.info("开始关闭所有SLS客户端");

        // 关闭健康检查调度器
        if (healthCheckScheduler != null && !healthCheckScheduler.isShutdown()) {
            healthCheckScheduler.shutdown();
            try {
                if (!healthCheckScheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    healthCheckScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                healthCheckScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭所有客户端
        for (String endpoint : clientCache.keySet()) {
            closeClient(endpoint);
        }
        log.info("所有SLS客户端已关闭");
    }

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        // 启动客户端健康检查
        startHealthCheck();
        log.info("SLS客户端工厂初始化完成");
    }

    /**
     * 获取客户端缓存大小
     *
     * @return 缓存大小
     */
    public int getCacheSize() {
        return clientCache.size();
    }

    /**
     * 清空客户端缓存
     */
    public void clearCache() {
        closeAllClients();
        clientCache.clear();
        log.info("SLS客户端缓存已清空");
    }

    /**
     * 带重试机制的操作执行
     *
     * @param operation 操作
     * @param <T>       返回类型
     * @return 操作结果
     */
    public <T> T executeWithRetry(Supplier<T> operation) {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount <= slsConfig.getMaxRetryCount()) {
            try {
                return operation.get();
            } catch (Exception e) {
                lastException = e;
                retryCount++;

                if (retryCount <= slsConfig.getMaxRetryCount()) {
                    log.warn("操作执行失败，第 {} 次重试，异常: {}", retryCount, e.getMessage());
                    try {
                        Thread.sleep(slsConfig.getRetryInterval() * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("操作执行失败，已达到最大重试次数: {}", slsConfig.getMaxRetryCount(), lastException);
        throw new RuntimeException("操作执行失败，已达到最大重试次数", lastException);
    }

    /**
     * 检查客户端连接健康状态
     *
     * @param client 客户端
     * @return 是否健康
     */
    private boolean isClientHealthy(Client client) {
        try {
            // 这里可以执行一个简单的操作来检查连接是否正常
            // 比如获取项目列表等轻量级操作
            return true;
        } catch (Exception e) {
            log.warn("客户端健康检查失败", e);
            return false;
        }
    }

    /**
     * 启动客户端健康检查
     */
    private void startHealthCheck() {
        healthCheckScheduler.scheduleAtFixedRate(() -> {
            try {
                log.debug("开始执行SLS客户端健康检查");
                clientCache.entrySet().removeIf(entry -> {
                    String endpoint = entry.getKey();
                    Client client = entry.getValue();

                    if (!isClientHealthy(client)) {
                        log.warn("客户端连接不健康，移除缓存，端点: {}", endpoint);
                        try {
                            client.shutdown();
                        } catch (Exception e) {
                            log.warn("关闭不健康客户端失败", e);
                        }
                        return true;
                    }
                    return false;
                });
                log.debug("SLS客户端健康检查完成");
            } catch (Exception e) {
                log.error("执行SLS客户端健康检查异常", e);
            }
        }, 5, 5, TimeUnit.MINUTES);
    }
}
