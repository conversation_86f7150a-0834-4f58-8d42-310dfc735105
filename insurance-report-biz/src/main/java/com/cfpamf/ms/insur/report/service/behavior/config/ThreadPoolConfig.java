package com.cfpamf.ms.insur.report.service.behavior.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Autowired
    private SlsConfig slsConfig;

    /**
     * SLS日志拉取专用线程池
     */
    @Bean("slsLogPullExecutor")
    public ThreadPoolTaskExecutor slsLogPullExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(slsConfig.getCorePoolSize());
        
        // 最大线程数
        executor.setMaxPoolSize(slsConfig.getMaxPoolSize());
        
        // 队列容量
        executor.setQueueCapacity(slsConfig.getQueueCapacity());
        
        // 线程名前缀
        executor.setThreadNamePrefix(slsConfig.getThreadNamePrefix());
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：调用者运行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化
        executor.initialize();
        
        log.info("SLS日志拉取线程池初始化完成，核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                slsConfig.getCorePoolSize(), slsConfig.getMaxPoolSize(), slsConfig.getQueueCapacity());
        
        return executor;
    }

    /**
     * 日志解析专用线程池
     */
    @Bean("logParseExecutor")
    public ThreadPoolTaskExecutor logParseExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数（CPU密集型任务，设置为CPU核心数）
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(corePoolSize);
        
        // 最大线程数
        executor.setMaxPoolSize(corePoolSize * 2);
        
        // 队列容量
        executor.setQueueCapacity(200);
        
        // 线程名前缀
        executor.setThreadNamePrefix("log-parse-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：调用者运行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化
        executor.initialize();
        
        log.info("日志解析线程池初始化完成，核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                corePoolSize, corePoolSize * 2, 200);
        
        return executor;
    }

    /**
     * 数据库批量操作专用线程池
     */
    @Bean("batchDbExecutor")
    public ThreadPoolTaskExecutor batchDbExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数（IO密集型任务，可以设置较多线程）
        executor.setCorePoolSize(3);
        
        // 最大线程数
        executor.setMaxPoolSize(8);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("batch-db-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：调用者运行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化
        executor.initialize();
        
        log.info("数据库批量操作线程池初始化完成，核心线程数: 3, 最大线程数: 8, 队列容量: 100");
        
        return executor;
    }
}
