package com.cfpamf.ms.insur.report.service.behavior.util;

import com.cfpamf.ms.insur.report.service.behavior.monitor.MemoryMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 批量处理工具类
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Component
public class BatchProcessor {

    @Autowired
    private MemoryMonitor memoryMonitor;

    /**
     * 默认批次大小
     */
    private static final int DEFAULT_BATCH_SIZE = 1000;

    /**
     * 批量处理数据（同步）
     *
     * @param dataList    数据列表
     * @param batchSize   批次大小
     * @param processor   处理器
     * @param <T>         数据类型
     */
    public <T> void processBatch(List<T> dataList, int batchSize, Consumer<List<T>> processor) {
        if (dataList == null || dataList.isEmpty()) {
            log.debug("数据列表为空，跳过批量处理");
            return;
        }

        int totalSize = dataList.size();
        log.info("开始批量处理数据，总数量: {}, 批次大小: {}", totalSize, batchSize);

        int processedCount = 0;
        int batchCount = 0;

        for (int i = 0; i < totalSize; i += batchSize) {
            // 检查内存使用情况
            if (memoryMonitor.isMemoryInDanger()) {
                log.warn("内存使用率过高，暂停处理并执行垃圾回收");
                memoryMonitor.forceGarbageCollection();
                
                // 等待一段时间
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            int endIndex = Math.min(i + batchSize, totalSize);
            List<T> batch = dataList.subList(i, endIndex);
            
            try {
                batchCount++;
                log.debug("处理第 {} 批数据，数量: {}", batchCount, batch.size());
                
                processor.accept(batch);
                processedCount += batch.size();
                
                log.debug("第 {} 批数据处理完成，已处理总数: {}/{}", batchCount, processedCount, totalSize);
                
            } catch (Exception e) {
                log.error("处理第 {} 批数据失败，数量: {}", batchCount, batch.size(), e);
                // 可以选择继续处理下一批或者抛出异常
                // throw new RuntimeException("批量处理失败", e);
            }
        }

        log.info("批量处理完成，总批次数: {}, 成功处理数量: {}/{}", batchCount, processedCount, totalSize);
    }

    /**
     * 批量处理数据（使用默认批次大小）
     *
     * @param dataList  数据列表
     * @param processor 处理器
     * @param <T>       数据类型
     */
    public <T> void processBatch(List<T> dataList, Consumer<List<T>> processor) {
        processBatch(dataList, DEFAULT_BATCH_SIZE, processor);
    }

    /**
     * 异步批量处理数据
     *
     * @param dataList      数据列表
     * @param batchSize     批次大小
     * @param processor     处理器
     * @param executorService 线程池
     * @param <T>           数据类型
     * @return CompletableFuture
     */
    public <T> CompletableFuture<Void> processBatchAsync(List<T> dataList, int batchSize, 
                                                        Consumer<List<T>> processor,
                                                        ExecutorService executorService) {
        return CompletableFuture.runAsync(() -> {
            processBatch(dataList, batchSize, processor);
        }, executorService);
    }

    /**
     * 批量转换数据
     *
     * @param dataList   数据列表
     * @param batchSize  批次大小
     * @param transformer 转换器
     * @param <T>        输入数据类型
     * @param <R>        输出数据类型
     * @return 转换后的数据列表
     */
    public <T, R> List<R> transformBatch(List<T> dataList, int batchSize, Function<List<T>, List<R>> transformer) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        List<R> resultList = new ArrayList<>();
        int totalSize = dataList.size();
        
        log.info("开始批量转换数据，总数量: {}, 批次大小: {}", totalSize, batchSize);

        for (int i = 0; i < totalSize; i += batchSize) {
            // 检查内存使用情况
            if (memoryMonitor.isMemoryInDanger()) {
                log.warn("内存使用率过高，暂停转换并执行垃圾回收");
                memoryMonitor.forceGarbageCollection();
                
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            int endIndex = Math.min(i + batchSize, totalSize);
            List<T> batch = dataList.subList(i, endIndex);
            
            try {
                List<R> batchResult = transformer.apply(batch);
                if (batchResult != null) {
                    resultList.addAll(batchResult);
                }
                
                log.debug("批量转换进度: {}/{}", endIndex, totalSize);
                
            } catch (Exception e) {
                log.error("批量转换失败，批次范围: {}-{}", i, endIndex, e);
                // 可以选择继续处理下一批或者抛出异常
            }
        }

        log.info("批量转换完成，输入数量: {}, 输出数量: {}", totalSize, resultList.size());
        return resultList;
    }

    /**
     * 批量转换数据（使用默认批次大小）
     *
     * @param dataList    数据列表
     * @param transformer 转换器
     * @param <T>         输入数据类型
     * @param <R>         输出数据类型
     * @return 转换后的数据列表
     */
    public <T, R> List<R> transformBatch(List<T> dataList, Function<List<T>, List<R>> transformer) {
        return transformBatch(dataList, DEFAULT_BATCH_SIZE, transformer);
    }

    /**
     * 分页处理数据
     *
     * @param totalCount  总数量
     * @param pageSize    页大小
     * @param dataFetcher 数据获取器（根据页码和页大小获取数据）
     * @param processor   处理器
     * @param <T>         数据类型
     */
    public <T> void processPages(int totalCount, int pageSize, 
                                Function<Integer, List<T>> dataFetcher,
                                Consumer<List<T>> processor) {
        if (totalCount <= 0) {
            log.debug("总数量为0，跳过分页处理");
            return;
        }

        int totalPages = (totalCount + pageSize - 1) / pageSize;
        log.info("开始分页处理数据，总数量: {}, 页大小: {}, 总页数: {}", totalCount, pageSize, totalPages);

        for (int page = 1; page <= totalPages; page++) {
            // 检查内存使用情况
            if (memoryMonitor.isMemoryInDanger()) {
                log.warn("内存使用率过高，暂停分页处理并执行垃圾回收");
                memoryMonitor.forceGarbageCollection();
                
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            try {
                log.debug("处理第 {}/{} 页数据", page, totalPages);
                
                List<T> pageData = dataFetcher.apply(page);
                if (pageData != null && !pageData.isEmpty()) {
                    processor.accept(pageData);
                    log.debug("第 {} 页数据处理完成，数量: {}", page, pageData.size());
                } else {
                    log.debug("第 {} 页数据为空", page);
                }
                
            } catch (Exception e) {
                log.error("处理第 {} 页数据失败", page, e);
                // 可以选择继续处理下一页或者抛出异常
            }
        }

        log.info("分页处理完成，总页数: {}", totalPages);
    }

    /**
     * 计算合适的批次大小（根据内存使用情况动态调整）
     *
     * @param defaultBatchSize 默认批次大小
     * @return 调整后的批次大小
     */
    public int calculateOptimalBatchSize(int defaultBatchSize) {
        double memoryUsageRatio = memoryMonitor.getCurrentMemoryUsageRatio();
        
        if (memoryUsageRatio > 0.8) {
            // 内存使用率超过80%，减小批次大小
            return Math.max(defaultBatchSize / 4, 100);
        } else if (memoryUsageRatio > 0.6) {
            // 内存使用率超过60%，适当减小批次大小
            return Math.max(defaultBatchSize / 2, 200);
        } else {
            // 内存使用率正常，使用默认批次大小
            return defaultBatchSize;
        }
    }
}
