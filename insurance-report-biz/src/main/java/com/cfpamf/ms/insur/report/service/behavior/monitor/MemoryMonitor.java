package com.cfpamf.ms.insur.report.service.behavior.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 内存监控器
 *
 * <AUTHOR>
 * @date 2025-09-30
 */
@Slf4j
@Component
public class MemoryMonitor {

    private final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    /**
     * 内存使用率阈值（80%）
     */
    private static final double MEMORY_THRESHOLD = 0.8;
    
    /**
     * 内存使用率危险阈值（90%）
     */
    private static final double MEMORY_DANGER_THRESHOLD = 0.9;

    @PostConstruct
    public void init() {
        // 每30秒监控一次内存使用情况
        scheduler.scheduleAtFixedRate(this::monitorMemory, 30, 30, TimeUnit.SECONDS);
        log.info("内存监控器启动成功，监控间隔: 30秒");
    }

    /**
     * 监控内存使用情况
     */
    public void monitorMemory() {
        try {
            MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemoryUsage = memoryMXBean.getNonHeapMemoryUsage();
            
            // 计算堆内存使用率
            double heapUsageRatio = (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax();
            
            // 记录内存使用情况
            if (heapUsageRatio > MEMORY_DANGER_THRESHOLD) {
                log.error("内存使用率过高！堆内存使用率: {:.2f}%, 已使用: {} MB, 最大: {} MB", 
                        heapUsageRatio * 100, 
                        heapMemoryUsage.getUsed() / 1024 / 1024,
                        heapMemoryUsage.getMax() / 1024 / 1024);
                
                // 触发垃圾回收
                System.gc();
                
                // 可以在这里添加告警逻辑
                sendMemoryAlert(heapUsageRatio);
                
            } else if (heapUsageRatio > MEMORY_THRESHOLD) {
                log.warn("内存使用率较高，堆内存使用率: {:.2f}%, 已使用: {} MB, 最大: {} MB", 
                        heapUsageRatio * 100, 
                        heapMemoryUsage.getUsed() / 1024 / 1024,
                        heapMemoryUsage.getMax() / 1024 / 1024);
            } else {
                log.debug("内存使用正常，堆内存使用率: {:.2f}%, 已使用: {} MB, 最大: {} MB", 
                        heapUsageRatio * 100, 
                        heapMemoryUsage.getUsed() / 1024 / 1024,
                        heapMemoryUsage.getMax() / 1024 / 1024);
            }
            
            // 记录非堆内存使用情况
            log.debug("非堆内存使用情况 - 已使用: {} MB, 已提交: {} MB", 
                    nonHeapMemoryUsage.getUsed() / 1024 / 1024,
                    nonHeapMemoryUsage.getCommitted() / 1024 / 1024);
            
        } catch (Exception e) {
            log.error("监控内存使用情况异常", e);
        }
    }

    /**
     * 获取当前内存使用率
     *
     * @return 内存使用率（0-1之间的小数）
     */
    public double getCurrentMemoryUsageRatio() {
        try {
            MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
            return (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax();
        } catch (Exception e) {
            log.error("获取内存使用率异常", e);
            return 0.0;
        }
    }

    /**
     * 检查内存是否充足
     *
     * @return 内存是否充足
     */
    public boolean isMemorySufficient() {
        return getCurrentMemoryUsageRatio() < MEMORY_THRESHOLD;
    }

    /**
     * 检查内存是否处于危险状态
     *
     * @return 内存是否处于危险状态
     */
    public boolean isMemoryInDanger() {
        return getCurrentMemoryUsageRatio() > MEMORY_DANGER_THRESHOLD;
    }

    /**
     * 获取内存使用详情
     *
     * @return 内存使用详情
     */
    public MemoryInfo getMemoryInfo() {
        try {
            MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemoryUsage = memoryMXBean.getNonHeapMemoryUsage();
            
            MemoryInfo memoryInfo = new MemoryInfo();
            memoryInfo.setHeapUsed(heapMemoryUsage.getUsed());
            memoryInfo.setHeapMax(heapMemoryUsage.getMax());
            memoryInfo.setHeapCommitted(heapMemoryUsage.getCommitted());
            memoryInfo.setNonHeapUsed(nonHeapMemoryUsage.getUsed());
            memoryInfo.setNonHeapCommitted(nonHeapMemoryUsage.getCommitted());
            memoryInfo.setUsageRatio(getCurrentMemoryUsageRatio());
            
            return memoryInfo;
        } catch (Exception e) {
            log.error("获取内存使用详情异常", e);
            return new MemoryInfo();
        }
    }

    /**
     * 发送内存告警
     *
     * @param usageRatio 内存使用率
     */
    private void sendMemoryAlert(double usageRatio) {
        // 这里可以实现具体的告警逻辑，比如发送邮件、短信等
        log.error("内存使用率告警：当前使用率 {:.2f}%，请及时处理！", usageRatio * 100);
    }

    /**
     * 强制垃圾回收
     */
    public void forceGarbageCollection() {
        log.info("执行强制垃圾回收");
        long beforeUsed = memoryMXBean.getHeapMemoryUsage().getUsed();
        System.gc();
        
        // 等待一段时间让GC完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long afterUsed = memoryMXBean.getHeapMemoryUsage().getUsed();
        long freed = beforeUsed - afterUsed;
        
        log.info("垃圾回收完成，释放内存: {} MB", freed / 1024 / 1024);
    }

    /**
     * 关闭监控器
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("内存监控器已关闭");
    }

    /**
     * 内存信息类
     */
    public static class MemoryInfo {
        private long heapUsed;
        private long heapMax;
        private long heapCommitted;
        private long nonHeapUsed;
        private long nonHeapCommitted;
        private double usageRatio;

        // Getters and Setters
        public long getHeapUsed() { return heapUsed; }
        public void setHeapUsed(long heapUsed) { this.heapUsed = heapUsed; }
        
        public long getHeapMax() { return heapMax; }
        public void setHeapMax(long heapMax) { this.heapMax = heapMax; }
        
        public long getHeapCommitted() { return heapCommitted; }
        public void setHeapCommitted(long heapCommitted) { this.heapCommitted = heapCommitted; }
        
        public long getNonHeapUsed() { return nonHeapUsed; }
        public void setNonHeapUsed(long nonHeapUsed) { this.nonHeapUsed = nonHeapUsed; }
        
        public long getNonHeapCommitted() { return nonHeapCommitted; }
        public void setNonHeapCommitted(long nonHeapCommitted) { this.nonHeapCommitted = nonHeapCommitted; }
        
        public double getUsageRatio() { return usageRatio; }
        public void setUsageRatio(double usageRatio) { this.usageRatio = usageRatio; }
        
        public long getHeapUsedMB() { return heapUsed / 1024 / 1024; }
        public long getHeapMaxMB() { return heapMax / 1024 / 1024; }
        public long getNonHeapUsedMB() { return nonHeapUsed / 1024 / 1024; }
    }
}
