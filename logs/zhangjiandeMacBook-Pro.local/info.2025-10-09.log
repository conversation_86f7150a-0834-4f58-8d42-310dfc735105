2025-10-09 00:57:19.434 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-10-09 00:57:20.643 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-10-09 00:57:20.645 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[675] No active profile set, falling back to default profiles: default
2025-10-09 00:57:23.168 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-10-09 00:57:23.176 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-10-09 00:57:23.272 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[182] Finished Spring Data repository scanning in 64ms. Found 0 repository interfaces.
2025-10-09 00:57:23.759 [] INFO [main] org.springframework.cloud.context.scope.GenericScope[295] BeanFactory id=c5d7be8f-3f7a-3429-b997-cca869dbdacd
2025-10-09 00:57:23.790 [] INFO [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor[48] Post-processing PropertySource instances
2025-10-09 00:57:23.859 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-10-09 00:57:23.859 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-10-09 00:57:23.860 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-10-09 00:57:23.860 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-10-09 00:57:23.860 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-10-09 00:57:23.860 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-10-09 00:57:23.861 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-10-09 00:57:23.861 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-10-09 00:57:23.861 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource application|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-10-09 00:57:23.861 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource insurance-report.yml|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-10-09 00:57:23.862 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/bootstrap.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-10-09 00:57:23.862 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-10-09 00:57:24.167 [] INFO [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[330] Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6c0df99f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
