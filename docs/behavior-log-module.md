# 用户行为日志拉取模块

## 概述

用户行为日志拉取模块是一个基于阿里云SLS（Log Service）的日志数据采集系统，通过定时任务从SLS中拉取用户行为日志数据，并存储到PostgreSQL数据库中。

## 功能特性

- **定时任务调度**：使用XXL-Job实现定时任务调度
- **分片处理**：支持SLS分片并行处理，提高数据拉取效率
- **游标管理**：自动管理SLS游标，支持断点续传
- **批量处理**：支持批量数据处理，防止内存溢出
- **性能监控**：内置内存监控和性能优化机制
- **重试机制**：支持失败重试，提高系统稳定性
- **灵活配置**：支持多种配置参数，适应不同场景

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   XXL-Job       │    │  SLS Log        │    │  PostgreSQL     │
│   定时任务       │───▶│  Service        │───▶│  数据库          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ BehaviorLogTask │    │   LogPullService│    │ UserBehaviorLog │
│ 任务管理         │    │   日志拉取服务   │    │ 用户行为日志     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 数据库表结构

### 1. 任务表 (behavior_log_task)
- 存储日志拉取任务的基本信息
- 包括项目名称、日志库名称、时间范围、查询条件等

### 2. 任务明细表 (behavior_log_task_detail)
- 存储每个分片的处理详情
- 包括分片ID、游标值、处理状态等

### 3. 用户行为日志表 (user_behavior_log)
- 存储从SLS拉取的用户行为数据
- 包括用户ID、事件类型、页面信息、设备信息等

## 配置说明

### application.yml 配置

```yaml
# xxl-job配置
xxl:
  job:
    admin:
      addresses: http://xxl-job-admin.dev.cfpamf.com/xxl-job-admin
    executor:
      appname: insurance-report-behavior-log
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
    accessToken: default_token

# 阿里云SLS配置
aliyun:
  sls:
    endpoint: cn-beijing.log.aliyuncs.com
    accessKeyId: ${ALIYUN_ACCESS_KEY_ID}
    accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET}
    batchSize: 1000
    maxProcessSize: 10000
    pullInterval: 5000
    maxRetryCount: 3
    enabled: true
    corePoolSize: 5
    maxPoolSize: 10
```

## 使用方法

### 1. 创建日志拉取任务

```bash
POST /api/behavior-log/task
Content-Type: application/json

{
  "taskName": "用户行为日志拉取任务",
  "projectName": "your-sls-project",
  "logstoreName": "your-logstore",
  "startTime": "2025-09-30T00:00:00",
  "endTime": "2025-09-30T23:59:59",
  "queryCondition": "event_type: click"
}
```

### 2. 启动任务

```bash
POST /api/behavior-log/task/{taskId}/start
```

### 3. 查询任务状态

```bash
GET /api/behavior-log/task/{taskId}/status
```

### 4. 查询任务进度

```bash
GET /api/behavior-log/task/{taskId}/progress
```

### 5. 查询用户行为日志

```bash
GET /api/behavior-log/logs?taskId={taskId}&limit=100
```

## 定时任务配置

在XXL-Job管理后台配置以下定时任务：

### 1. 日志拉取任务
- **JobHandler**: `behaviorLogPullHandler`
- **Cron**: `0 */5 * * * ?` (每5分钟执行一次)
- **描述**: 查询需要执行的任务并启动日志拉取

### 2. 日志清理任务
- **JobHandler**: `behaviorLogCleanHandler`
- **Cron**: `0 0 2 * * ?` (每天凌晨2点执行)
- **参数**: `30` (清理30天前的数据)
- **描述**: 清理过期的日志数据

### 3. 任务监控任务
- **JobHandler**: `behaviorLogMonitorHandler`
- **Cron**: `0 */10 * * * ?` (每10分钟执行一次)
- **描述**: 监控长时间运行的任务

## 性能优化

### 1. 内存管理
- 内置内存监控器，实时监控内存使用情况
- 当内存使用率超过80%时自动调整批处理大小
- 支持强制垃圾回收

### 2. 批量处理
- 支持批量数据处理，默认批次大小1000条
- 根据内存使用情况动态调整批次大小
- 分批插入数据库，避免大事务

### 3. 线程池管理
- 日志拉取专用线程池
- 日志解析专用线程池
- 数据库批量操作专用线程池

### 4. 连接池管理
- SLS客户端连接池
- 自动健康检查和连接重建
- 支持连接重试机制

## 监控和告警

### 1. 内存监控
- 实时监控堆内存使用率
- 内存使用率超过90%时发送告警
- 支持手动触发垃圾回收

### 2. 任务监控
- 监控任务执行状态和进度
- 检测长时间运行的任务
- 记录任务执行日志

### 3. 性能指标
- 日志拉取速度
- 数据处理量统计
- 错误率统计

## 故障排查

### 1. 常见问题

**问题1**: 任务启动失败
- 检查SLS配置是否正确
- 检查网络连接是否正常
- 查看应用日志获取详细错误信息

**问题2**: 内存溢出
- 调整批处理大小
- 增加JVM堆内存
- 检查是否有内存泄漏

**问题3**: 数据拉取缓慢
- 检查SLS分片数量
- 调整线程池大小
- 优化数据库插入性能

### 2. 日志查看

应用日志路径：`/data/applogs/insurance-report/`
XXL-Job日志路径：`/data/applogs/xxl-job/jobhandler/`

## 注意事项

1. **权限配置**：确保SLS访问密钥有足够的权限
2. **网络连接**：确保应用服务器能够访问SLS服务
3. **数据库连接**：确保数据库连接池配置合理
4. **磁盘空间**：定期清理过期日志，避免磁盘空间不足
5. **监控告警**：建议配置监控告警，及时发现问题

## 版本历史

- **v1.0.0** (2025-09-30)
  - 初始版本发布
  - 支持基本的日志拉取功能
  - 集成XXL-Job定时任务
  - 内置性能监控和优化机制
